'use client';

import Link from 'next/link';
import { useEffect, useState, use, useRef } from 'react';
import PDFViewer from '@/components/PDFViewer';
import MarkdownViewer from '@/components/MarkdownViewer';
import { InformationCircleIcon } from '@heroicons/react/24/outline';
import Tooltip from '@/components/Tooltip';
import PaperNavigation from '@/components/PaperNavigation';

// Complete mapping of paper IDs to their file names
const knownPaperFiles: { [key: string]: string } = {
  // Papers 1-10
  'adaptive_temperature_sampling': '1_Adaptive_Temperature_Sampling',
  'can_chatgpt_replace_stackoverflow': '2_Can_ChatGPT_replace_StackOverflow',
  'chain_of_verification': '3_Chain_of_Verification',
  'enhancing_zero_shot_chain_of_thought_reasoning_in_large': '4_Enhancing_Zero_Shot_Chain_of_Thought_Reasoning_in_Large',
  'few_shot_fine_tuning_vs_in_context_learning_a_fair_comparison_and_evaluation': '5_Few_shot_Fine_tuning_vs_In_context_Learning_A_Fair_Comparison_and_Evaluation',
  'from_sparse_to_dense_gpt_4_summarization_with_chain_of_density_prompting': '6_From_Sparse_to_Dense_GPT_4_Summarization_with_Chain_of_Density_Prompting',
  'graph_of_thoughts': '7_Graph_of_Thoughts',
  'harnessing_the_power_of_llms_in_practice_a_survey_on_chatgpt_and_beyond': '8_Harnessing_the_Power_of_LLMs_in_Practice_A_Survey_on_ChatGPT_and_Beyond',
  'less_likely_brainstorming': '9_Less_Likely_Brainstorming',
  'meta_in_context_learning': '10_Meta_in_context_learning',

  // Papers 11-21
  'plan_and_solve_prompting': '11_Plan_and_Solve_Prompting',
  're_reading_improves_reasoning_in_large_language_models': '12_Re_Reading_Improves_Reasoning_in_Large_Language_Models',
  'reinforcement_learning_in_the_era_of_llms': '13_Reinforcement_Learning_in_the_Era_of_LLMs',
  'self_taught_optimizer_stop': '14_Self_Taught_Optimizer_STOP',
  'summarization_is_almost_dead': '15_Summarization_is_Almost_Dead',
  'the_prompt_report_a_systematic_survey_of_prompting_techniques': '16_The_Prompt_Report_A_Systematic_Survey_of_Prompting_Techniques',
  'think_before_you_speak': '17_Think_before_you_speak',
  'treeprompt': '18_TreePrompt',
  'walking_through_the_memory_maze_beyond': '19_Walking_Through_The_Memory_Maze_Beyond',
  'why_think_step_by_step': '20_Why_think_step_by_step',
  'xavier_amatriain_prompt_design_and_engineering_introduction_and_advanced_methods': '21_Xavier_Amatriain_Prompt_Design_and_Engineering_Introduction_and_Advanced_Methods'
};

export default function PaperView({ params }: { params: Promise<{ paper: string }> }) {
  const resolvedParams = use(params);
  const [markdownContent, setMarkdownContent] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paperTitle, setPaperTitle] = useState('');
  const [leftPanelWidth, setLeftPanelWidth] = useState(50); // Default 50% width for left panel
  const containerRef = useRef<HTMLDivElement>(null);

  const paperId = resolvedParams.paper;
  console.log('Paper ID:', paperId);

  useEffect(() => {
    // First, try to load the evaluation JSON to get paper metadata
    const loadPaperMetadata = async () => {
      try {
        // Convert paperId to proper filename format for the evaluation JSON
        // Example: 'adaptive_temperature_sampling' -> 'Adaptive_Temperature_Sampling'
        const evaluationFile = paperId
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join('_');

        // Try the new directory structure with Gemini suffix
        const jsonPath = `/papers/evaluations/${evaluationFile}/Evaluation_${evaluationFile}_Gemini.json`;
        console.log('Loading JSON from:', jsonPath);

        const response = await fetch(jsonPath);
        if (!response.ok) {
          // Try the old path format as fallback
          const oldJsonPath = `/papers/evaluations/Evaluation_${evaluationFile}.json`;
          console.log('Trying fallback path:', oldJsonPath);

          const fallbackResponse = await fetch(oldJsonPath);
          if (!fallbackResponse.ok) {
            throw new Error(`Failed to load evaluation JSON for ${paperId}`);
          }

          const data = await fallbackResponse.json();
          setPaperTitle(data.metadata.title);
          return;
        }

        const data = await response.json();
        setPaperTitle(data.metadata.title);
      } catch (err) {
        console.error('Error loading JSON evaluation:', err);
        // If we can't load the JSON, we'll try to continue with the file mapping
        // Set a fallback title based on the paper ID
        const formattedTitle = paperId
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        setPaperTitle(formattedTitle);
      }
    };

    loadPaperMetadata();
  }, [paperId]);

  // Get the file name for the paper content
  let encodedFile = '';

  // Check if we have a mapping for this paper ID
  if (knownPaperFiles[paperId]) {
    encodedFile = knownPaperFiles[paperId];
  } else {
    // If no mapping exists, try to generate a filename based on the ID
    // This assumes the file naming convention is consistent
    // Map common paper IDs to their file numbers
    const paperNumberMap: { [key: string]: string } = {
      'adaptive_temperature_sampling': '1',
      'can_chatgpt_replace_stackoverflow': '2',
      'chain_of_verification': '3',
      'enhancing_zero_shot_chain_of_thought': '4',
      'few_shot_fine_tuning_vs_in_context': '5',
      'from_sparse_to_dense': '6',
      'graph_of_thoughts': '7',
      'harnessing_llms_in_practice': '8',
      'less_likely_brainstorming': '9'
    };

    const fileNumber = paperNumberMap[paperId] || '1';

    encodedFile = fileNumber + '_' + paperId
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('_');
  }

  const pdfUrl = `/papers/pdf/${encodedFile}.pdf`;
  const markdownPath = `/papers/${encodedFile}.md`;

  console.log('PDF URL:', pdfUrl);
  console.log('Markdown path:', markdownPath);

  useEffect(() => {
    setLoading(true);
    console.log('Loading markdown from:', markdownPath);

    fetch(markdownPath)
      .then((response) => {
        if (!response.ok) {
          throw new Error('Failed to load markdown content');
        }
        return response.text();
      })
      .then((content) => {
        setMarkdownContent(content);
        setLoading(false);
        setError(null);
      })
      .catch((err) => {
        console.error('Error loading markdown:', err);
        setError('Failed to load paper content');
        setLoading(false);
      });
  }, [markdownPath]);

  // If we couldn't load the paper data, show an error
  if (!encodedFile) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-black font-roboto">
        {/* Paper Navigation */}
        <PaperNavigation paperSlug={paperId} paperTitle={paperTitle} />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4 text-gray-900 dark:text-gray-100">Paper Not Found</h1>
            <p className="text-gray-800 dark:text-gray-200 mb-8">The requested paper could not be found.</p>
            <Link
              href="/critical-review/source-material"
              className="text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
            >
              ← Back to Source Material
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-zinc-900 font-roboto">
      {/* Paper Navigation */}
      <PaperNavigation paperSlug={paperId} paperTitle={paperTitle} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Navigation */}
      <div className="mb-8">
        <Link
          href="/critical-review/source-material"
          className="text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 flex items-center"
        >
          ← Back to Source Material
        </Link>
      </div>

      {/* Paper Title */}
      <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 border-t-0 mb-8">
        <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
        <div className="p-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">{paperTitle || 'Loading paper title...'}</h1>
          <div className="flex justify-between items-center">
            <Link
              href={`/critical-review/evaluation/${paperId}`}
              className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 font-medium"
            >
              View Evaluation →
            </Link>
          </div>
        </div>
      </div>

      {/* Paper Content */}
      <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 border-t-0 overflow-hidden">
        <div className="h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
        {loading ? (
          <div className="p-8 text-center">
            <p className="text-gray-600 dark:text-gray-400">Loading paper content...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <p className="text-red-600 dark:text-red-400">{error}</p>
          </div>
        ) : (
          <div className="flex flex-col lg:flex-row relative h-[calc(100vh-12rem)]" ref={containerRef}>
            {/* Left Panel - PDF Viewer */}
            <div
              style={{
                width: `${leftPanelWidth}%`,
                transition: 'width 0.1s ease-out'
              }}
              className="flex flex-col h-full"
            >
              <div className="bg-white dark:bg-zinc-900 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 p-6">
                  PDF Viewer
                </h2>
              </div>
              <div className="bg-gray-50 dark:bg-black/30 flex-grow overflow-hidden">
                <PDFViewer url={pdfUrl} />
              </div>
            </div>

            {/* Draggable Divider */}
            <div
              className="w-2 lg:w-1 h-full bg-gradient-to-b from-blue-500 to-purple-500 hover:from-blue-400 hover:to-purple-400 cursor-col-resize flex-shrink-0 relative z-10 transition-all duration-200 ease-out"
              onMouseDown={(e) => {
                e.preventDefault();
                e.stopPropagation();

                // Add visual feedback during drag
                document.body.style.cursor = 'col-resize';
                document.body.style.userSelect = 'none';

                // Store initial position
                const initialX = e.clientX;
                const initialLeftWidth = leftPanelWidth;

                // Create a function to handle mouse movement
                function onMouseMove(moveEvent: MouseEvent) {
                  moveEvent.preventDefault();
                  moveEvent.stopPropagation();

                  if (!containerRef.current) return;

                  // Calculate how far the mouse has moved
                  const dx = moveEvent.clientX - initialX;

                  // Calculate the new width as a percentage
                  const containerWidth = containerRef.current.clientWidth;
                  const percentageMoved = (dx / containerWidth) * 100;
                  const newLeftWidth = Math.min(Math.max(15, initialLeftWidth + percentageMoved), 85);

                  // Update state
                  setLeftPanelWidth(newLeftWidth);
                }

                // Create a function to handle mouse up
                function onMouseUp(upEvent: MouseEvent) {
                  upEvent.preventDefault();
                  upEvent.stopPropagation();

                  // Reset cursor and selection
                  document.body.style.cursor = '';
                  document.body.style.userSelect = '';

                  // Remove event listeners
                  document.removeEventListener('mousemove', onMouseMove);
                  document.removeEventListener('mouseup', onMouseUp);
                }

                // Add event listeners
                document.addEventListener('mousemove', onMouseMove);
                document.addEventListener('mouseup', onMouseUp);
              }}
              onTouchStart={(e) => {
                e.preventDefault();
                e.stopPropagation();

                const touch = e.touches[0];
                const initialX = touch.clientX;
                const initialLeftWidth = leftPanelWidth;

                function onTouchMove(moveEvent: TouchEvent) {
                  moveEvent.preventDefault();
                  moveEvent.stopPropagation();

                  if (!containerRef.current) return;

                  const touch = moveEvent.touches[0];
                  const dx = touch.clientX - initialX;

                  const containerWidth = containerRef.current.clientWidth;
                  const percentageMoved = (dx / containerWidth) * 100;
                  const newLeftWidth = Math.min(Math.max(15, initialLeftWidth + percentageMoved), 85);

                  setLeftPanelWidth(newLeftWidth);
                }

                function onTouchEnd(upEvent: TouchEvent) {
                  upEvent.preventDefault();
                  upEvent.stopPropagation();

                  document.removeEventListener('touchmove', onTouchMove as any);
                  document.removeEventListener('touchend', onTouchEnd as any);
                }

                document.addEventListener('touchmove', onTouchMove as any);
                document.addEventListener('touchend', onTouchEnd as any);
              }}
            />

            {/* Right Panel - Markdown Viewer */}
            <div
              style={{
                width: `${100 - leftPanelWidth}%`,
                transition: 'width 0.1s ease-out'
              }}
              className="flex flex-col h-full"
            >
              <div className="bg-white dark:bg-zinc-900 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 p-6 flex items-center justify-between">
                  <span>Markdown Version</span>
                  <Tooltip text="This text is the original pdf file converted to markdown using the marker-pdf neural conversion model to allow better compatibility for analysis.">
                    <InformationCircleIcon
                      className="h-6 w-6 text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 cursor-help transition-colors duration-200"
                    />
                  </Tooltip>
                </h2>
              </div>
              <div className="p-8 overflow-y-auto flex-grow bg-white dark:bg-zinc-900">
                <div className="prose prose-sm dark:prose-invert max-w-none text-gray-700 dark:text-gray-300 leading-relaxed font-roboto">
                  <MarkdownViewer content={markdownContent} />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
    </div>
  );
}
